from rest_framework import serializers
from course.models import Course

class PowerBICourseSerializer(serializers.ModelSerializer):
    """Serializer for Power BI course data with specific field mapping"""
    university = serializers.CharField(source='universityName')
    faculty = serializers.Char<PERSON>ield(source='facultyName')
    department = serializers.CharField()
    courseNameTh = serializers.CharField(source='programTh')
    courseNameEn = serializers.CharField(source='programEn')
    courseType = serializers.CharField(source='masCourseType.name')
    courseDetails = serializers.CharField(source='detail')
    studentsPerYear = serializers.CharField(source='numberOfStudent')
    courseWebsite = serializers.Char<PERSON>ield(source='link')
    coordinator = serializers.CharField(source='contactName')
    coordinatorEmail = serializers.CharField(source='contactEmail')

    class Meta:
        model = Course
        fields = [
            'university', 'faculty', 'department', 'courseNameTh',
            'courseNameEn', 'courseType', 'courseDetails', 'studentsPerYear',
            'courseWebsite', 'coordinator', 'coordinatorEmail'
        ]
