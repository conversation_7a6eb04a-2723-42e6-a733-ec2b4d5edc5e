from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from course.models import Course
from .serializers import PowerBICourseSerializer


@extend_schema(
    tags=["Power BI"],
    description="""
    Retrieve course data for Power BI integration.

    **Authentication Required**: This endpoint requires API key authentication.

    **Headers Required**:
    - `x-api-key`: Your PowerBI API key

    **Example Request**:
    ```
    GET /api/powerbi/course/
    Headers:
        x-api-key: your-api-key-here
    ```

    **Error Responses**:
    - `401 Unauthorized`: Missing or invalid API key
    - `500 Internal Server Error`: Server error
    """,
    parameters=[
        OpenApiParameter(
            name='x-api-key',
            type=str,
            location=OpenApiParameter.HEADER,
            required=True,
            description='API key for PowerBI authentication'
        ),
    ],
    responses={
        200: {
            "description": "Course data retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "data": [
                            {
                                "id": 1,
                                "title": "Sample Course",
                                "description": "Course description"
                            }
                        ],
                        "total": 1,
                        "version": "v.1.1.0"
                    }
                }
            }
        },
        401: {
            "description": "Authentication failed",
            "content": {
                "application/json": {
                    "example": {
                        "status": "error",
                        "code": "missing_api_key",
                        "message": "API key is required",
                        "version": "v.1.1.0"
                    }
                }
            }
        }
    }
)
@api_view(['GET'])
@permission_classes([AllowAny])
def get_course(request):
    try:
        courses = Course.objects.select_related('masCourseType').order_by('-id')
        total = courses.count()

        return Response({
            "data": PowerBICourseSerializer(courses, many=True).data,
            "total": total,
        }, status=200)
    except Exception as e:
        return Response({
            "error": "Error",
            "message": str(e)
        }, status=500)
